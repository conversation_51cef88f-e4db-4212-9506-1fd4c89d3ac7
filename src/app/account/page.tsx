"use client"
import React from "react";

const AccountPage: React.FC = () => {
  // Dummy user data for demonstration
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    plan: "Pro",
    joined: "2024-01-15",
    avatar: "https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=0D8ABC&color=fff"
  };

  return (
    <div className="max-w-2xl mx-auto p-8 bg-white rounded-lg shadow-md mt-10">
      <div className="flex items-center mb-8">
        <img
          src={user.avatar}
          alt="User Avatar"
          className="w-20 h-20 rounded-full border-4 border-blue-200 shadow-sm mr-6"
        />
        <div>
          <h2 className="text-2xl font-bold text-gray-800">{user.name}</h2>
          <p className="text-gray-500">{user.email}</p>
          <span className="inline-block mt-2 px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
            {user.plan} Plan
          </span>
        </div>
      </div>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Account Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="block text-gray-500 text-xs">Full Name</span>
            <span className="block text-gray-800 font-medium">{user.name}</span>
          </div>
          <div>
            <span className="block text-gray-500 text-xs">Email Address</span>
            <span className="block text-gray-800 font-medium">{user.email}</span>
          </div>
          <div>
            <span className="block text-gray-500 text-xs">Plan</span>
            <span className="block text-gray-800 font-medium">{user.plan}</span>
          </div>
          <div>
            <span className="block text-gray-500 text-xs">Member Since</span>
            <span className="block text-gray-800 font-medium">{user.joined}</span>
          </div>
        </div>
      </div>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Security</h3>
        <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition">Change Password</button>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Danger Zone</h3>
        <button className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition">Delete Account</button>
      </div>
    </div>
  );
};

export default AccountPage;
