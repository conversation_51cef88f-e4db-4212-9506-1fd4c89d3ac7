"use client"
import Link from 'next/link';
import React from 'react';
import { Bell, HelpCircle, User, Globe, PlusCircle, Settings, LayoutDashboard, CreditCard, Link as LinkIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../components/providers/AuthProvider';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { user, loading, signOut } = useAuth();
  const [signingOut, setSigningOut] = React.useState(false);


  return (
    <div className="flex h-screen bg-[#eaf3e1]">
      {/* Sidebar */}
      <aside className="w-64 bg-[#3d5c3a] text-white flex flex-col justify-between py-6 px-0 fixed inset-y-0 left-0 z-30">
        <div>
          <div className="flex items-center px-8 mb-10">
            <div className="bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3">
              <span className="text-2xl font-bold text-[#3d5c3a]">W</span>
            </div>
            <span className="text-2xl font-bold tracking-tight">AI builder</span>
          </div>
          <nav>
            <ul className="space-y-1">
              <li>
                <Link href="/dashboard" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/websites' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}> 
                  <Globe className="w-5 h-5" />
                  <span className="ml-4">Websites</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/create" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/create' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}> 
                  <PlusCircle className="w-5 h-5" />
                  <span className="ml-4">Create</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/domain" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/domain' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <LinkIcon className="w-5 h-5" />
                  <span className="ml-4">Domain</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/account" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/account' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}> 
                  <User className="w-5 h-5" />
                  <span className="ml-4">Account</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/billing" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/billing' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <CreditCard className="w-5 h-5" />
                  <span className="ml-4">Billing</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/settings" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/settings' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}> 
                  <Settings className="w-5 h-5" />
                  <span className="ml-4">Settings</span>
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex flex-col flex-1 min-h-screen ml-64 overflow-x-auto">
        {/* Header */}
        <header className="sticky top-0 z-10 bg-white border-b border-gray-200">
          <div className="flex items-center justify-end h-16 p-4">
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full hover:bg-gray-100">
                <Bell className="w-6 h-6 text-gray-600" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100">
                <HelpCircle className="w-6 h-6 text-gray-600" />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-100">
                <User className="w-6 h-6 text-gray-600" />
              </button>
              {/* Show user email */}
              <span className="ml-2 text-gray-700 font-medium">
                {loading ? 'Loading...' : user?.email || 'Not signed in'}
              </span>
              {/* Sign Out Button */}
              <button
                className="ml-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-300 disabled:text-gray-500"
                onClick={async () => {
                  setSigningOut(true);
                  await signOut();
                  setSigningOut(false);
                  router.replace('/login');
                }}
                disabled={signingOut}
              >
                {signingOut ? 'Signing out...' : 'Sign Out'}
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-6 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}