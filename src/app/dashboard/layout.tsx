"use client"
import Link from 'next/link';
import React from 'react';
import { Bell, HelpCircle, User, Globe, PlusCircle, Settings, CreditCard, Link as LinkIcon, Menu, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../components/providers/AuthProvider';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { user, loading, signOut } = useAuth();
  const [signingOut, setSigningOut] = React.useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);


  return (
    <div className="flex h-screen bg-[#eaf3e1]">
      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar - Desktop */}
      <aside className="hidden lg:flex w-64 bg-[#3d5c3a] text-white flex-col justify-between py-6 px-0 fixed inset-y-0 left-0 z-30">
        <div>
          <div className="flex items-center px-8 mb-10">
            <div className="bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3">
              <span className="text-2xl font-bold text-[#3d5c3a]">W</span>
            </div>
            <span className="text-2xl font-bold tracking-tight">AI builder</span>
          </div>
          <nav>
            <ul className="space-y-1">
              <li>
                <Link href="/dashboard" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <Globe className="w-5 h-5" />
                  <span className="ml-4">Websites</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/create" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/create' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <PlusCircle className="w-5 h-5" />
                  <span className="ml-4">Create</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/domain" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/domain' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <LinkIcon className="w-5 h-5" />
                  <span className="ml-4">Domain</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/account" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/account' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <User className="w-5 h-5" />
                  <span className="ml-4">Account</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/billing" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/billing' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <CreditCard className="w-5 h-5" />
                  <span className="ml-4">Billing</span>
                </Link>
              </li>
              <li>
                <Link href="/dashboard/settings" className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/settings' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}>
                  <Settings className="w-5 h-5" />
                  <span className="ml-4">Settings</span>
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <aside className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-[#3d5c3a] text-white transform transition-transform duration-300 ease-in-out ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="flex flex-col h-full py-6">
          <div className="flex items-center justify-between px-8 mb-10">
            <div className="flex items-center">
              <div className="bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3">
                <span className="text-2xl font-bold text-[#3d5c3a]">W</span>
              </div>
              <span className="text-2xl font-bold tracking-tight">AI builder</span>
            </div>
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="p-2 rounded-md hover:bg-[#4e6e4a] transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          <nav className="flex-1">
            <ul className="space-y-1">
              <li>
                <Link
                  href="/dashboard"
                  className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Globe className="w-5 h-5" />
                  <span className="ml-4">Websites</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/create"
                  className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/create' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <PlusCircle className="w-5 h-5" />
                  <span className="ml-4">Create</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/domain"
                  className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/domain' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <LinkIcon className="w-5 h-5" />
                  <span className="ml-4">Domain</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/account"
                  className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/account' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <User className="w-5 h-5" />
                  <span className="ml-4">Account</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/billing"
                  className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/billing' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <CreditCard className="w-5 h-5" />
                  <span className="ml-4">Billing</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/settings"
                  className={`flex items-center px-8 py-3 rounded-l-full transition-colors ${typeof window !== 'undefined' && window.location.pathname === '/dashboard/settings' ? 'bg-white text-[#3d5c3a] font-semibold shadow' : 'hover:bg-[#4e6e4a] hover:text-white'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Settings className="w-5 h-5" />
                  <span className="ml-4">Settings</span>
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex flex-col flex-1 min-h-screen overflow-x-auto lg:ml-64">
        {/* Header */}
        <header className="sticky top-0 z-10 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="p-2 text-gray-600 rounded-md hover:bg-gray-100 lg:hidden"
            >
              <Menu className="w-6 h-6" />
            </button>

            {/* Desktop header content */}
            <div className="hidden lg:block" />

            {/* Header actions */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button className="hidden p-2 rounded-full hover:bg-gray-100 sm:block">
                <Bell className="w-5 h-5 text-gray-600 sm:w-6 sm:h-6" />
              </button>
              <button className="hidden p-2 rounded-full hover:bg-gray-100 sm:block">
                <HelpCircle className="w-5 h-5 text-gray-600 sm:w-6 sm:h-6" />
              </button>
              <button className="hidden p-2 rounded-full hover:bg-gray-100 sm:block">
                <User className="w-5 h-5 text-gray-600 sm:w-6 sm:h-6" />
              </button>
              {/* Show user email - responsive */}
              <span className="hidden ml-2 text-sm font-medium text-gray-700 md:block lg:text-base">
                {loading ? 'Loading...' : user?.email || 'Not signed in'}
              </span>
              {/* Sign Out Button - responsive */}
              <button
                className="px-2 py-1 text-sm text-white bg-red-500 rounded sm:px-4 sm:py-2 hover:bg-red-600 disabled:bg-gray-300 disabled:text-gray-500 sm:text-base"
                onClick={async () => {
                  setSigningOut(true);
                  await signOut();
                  setSigningOut(false);
                  router.replace('/login');
                }}
                disabled={signingOut}
              >
                <span className="hidden sm:inline">
                  {signingOut ? 'Signing out...' : 'Sign Out'}
                </span>
                <span className="sm:hidden">
                  {signingOut ? '...' : 'Out'}
                </span>
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-4 overflow-y-auto sm:p-6">
          {children}
        </main>
      </div>
    </div>
  );
}