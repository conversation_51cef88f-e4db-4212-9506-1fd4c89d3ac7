"use client";

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useAuth } from '../../../components/providers/AuthProvider';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

interface Site {
  id: string;
  site_name: string;
  expiry_status: 'Permanent' | 'Temporary';
  expiry_time?: string;
}

const DomainPage = () => {
  const { user, loading: authLoading } = useAuth();
  const [domainName, setDomainName] = useState('');
  const [results, setResults] = useState<DomainCheckResult[]>([]);
  const [loading, setLoading] = useState(false); // for domain search
  const [registering, setRegistering] = useState<string | null>(null);
  const [sites, setSites] = useState<Site[]>([]);
  const [selectedSiteId, setSelectedSiteId] = useState<string>('');
  const [sitesLoading, setSitesLoading] = useState(true);
  const supabase = createClientComponentClient();

  // Fetch sites on component mount
  useEffect(() => {
    if (!authLoading && !user) {
      window.location.href = '/login';
    }
  }, [user, authLoading]);

  useEffect(() => {
    const fetchSites = async () => {
      try {
        const { data, error } = await supabase
          .from('user-websites')
          .select('id, site_name, expiry_status, expiry_time');

        if (error) {
          throw error;
        }

        setSites(data as Site[]);
      } catch (err) {
        console.error('Error fetching sites:', err);
      } finally {
        setSitesLoading(false);
      }
    };

    fetchSites();
  }, [supabase]);

  const handleSearch = async (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    if (!domainName.trim()) return;

    setLoading(true);
    setResults([]);

    try {
      const response = await fetch('/api/namecheap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain: domainName.trim(), action: 'check' }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to check domain availability.');
      }

      const data = await response.json();
      setResults(data.results);
      console.log("data.results",data.results);
    } catch (error) {
      const err = error as Error;
      console.error('Domain search error:', err);
      setResults([{ Domain: domainName, Available: false, IsPremiumName: false, Error: err.message }]);
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (domain: string) => {
    if (!selectedSiteId) {
      alert('Please select a site to associate with this domain.');
      return;
    }

    setRegistering(domain);

    // Find the price for the selected domain
    const domainResult = results.find(r => r.Domain === domain);
    const price = domainResult?.Price;
    if (!price) {
      alert('Could not determine the price for this domain.');
      setRegistering(null);
      return;
    }

    try {
      const response = await fetch('/api/domain-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          domain,
          price,
          siteId: selectedSiteId
        }),
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || 'Failed to initiate payment.');
      }

      const { url } = await response.json();
      if (url) {
        window.location.href = url;
      } else {
        throw new Error('No Stripe Checkout URL returned.');
      }
    } catch (error) {
      const err = error as Error;
      console.error('Domain payment initiation error:', err);
      alert(`Failed to initiate payment for ${domain}: ${err.message}`);
      setRegistering(null);
    }
  };

  if (authLoading || !user) {
    return <div className="py-8 text-center">Checking authentication...</div>;
  }
  if (sitesLoading) {
    return <div className="py-8 text-center">Loading sites...</div>;
  }

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto">
        <h1 className="mb-4 text-4xl font-bold text-gray-800">Register a Domain</h1>
        <p className="mb-8 text-gray-600">Find and register the perfect domain for your new website.</p>

        {/* Site Selection */}
        <div className="p-6 mb-8 bg-white rounded-lg shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">Select Site</h2>
          <p className="mb-4 text-gray-600">Choose which site you want to associate with your new domain.</p>

          {sitesLoading ? (
            <div className="text-gray-500">Loading sites...</div>
          ) : sites.length === 0 ? (
            <div className="text-gray-500">No sites available. Please create a site first.</div>
          ) : (
            <select
              value={selectedSiteId}
              onChange={(e) => setSelectedSiteId(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="">Select a site...</option>
              {sites.map((site) => (
                <option key={site.id} value={site.id}>
                  {site.site_name} ({site.expiry_status})
                </option>
              ))}
            </select>
          )}
        </div>

        <div className="p-6 mb-8 bg-white rounded-lg shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">Search for Domain</h2>
          <p className="mb-4 text-gray-600">Enter a domain name to check availability across multiple extensions.</p>

          {!selectedSiteId && (
            <div className="p-3 mb-4 border rounded-md text-amber-700 bg-amber-50 border-amber-200">
              <p className="text-sm">⚠️ Please select a site above before searching for domains.</p>
            </div>
          )}

          <div className="flex items-center gap-4">
            <input
              type="text"
              value={domainName}
              onChange={(e) => setDomainName(e.target.value)}
              placeholder="Find your new domain (e.g., my-awesome-site)"
              className="flex-grow p-3 transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
            />
            <button
              onClick={handleSearch}
              disabled={loading || !domainName.trim() || !selectedSiteId}
              className="px-6 py-3 font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              title={!selectedSiteId ? 'Please select a site first' : ''}
            >
              {loading ? 'Searching...' : 'Search'}
            </button>
          </div>
        </div>

        {results.length > 0 && (
          <div className="p-6 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold text-gray-800">Results</h2>
              {selectedSiteId && (
                <div className="text-sm text-gray-600">
                  Will be associated with: <span className="font-medium">{sites.find(s => s.id === selectedSiteId)?.site_name}</span>
                </div>
              )}
            </div>
            <ul className="space-y-4">
              {results.map((result) => (
                <li key={result.Domain} className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex flex-col">
                    <span className="text-lg font-medium text-gray-700">{result.Domain}</span>
                    {result.Price && (
                      <span className="text-sm text-gray-500">
                        AUD ${result.Price.toFixed(2)}/year
                        {result.IsPremiumName && <span className="ml-2 font-semibold text-orange-500">Premium</span>}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-4">
                    {result.Error ? (
                      <span className="font-semibold text-red-500">{result.Error}</span>
                    ) : result.Available ? (
                      <>
                        <span className="font-bold text-green-600">Available!</span>
                        <button
                          onClick={() => handleRegister(result.Domain)}
                          disabled={registering === result.Domain}
                          className="px-4 py-2 font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[100px]"
                        >
                          {registering === result.Domain ? 'Registering...' : 'Register'}
                        </button>
                      </>
                    ) : (
                      <span className="font-semibold text-red-500">Unavailable</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

      </div>
    </div>
  );
};

export default DomainPage;