"use client"

import React, { useState } from 'react';
import { Camera, Edit3, Save, X, User, Mail, Phone, MapPin, Calendar, Shield, Bell, Lock } from 'lucide-react';
import { useAuth } from '../../../components/providers/AuthProvider';

export default function ProfilePage() {
  const { user, session, loading: authLoading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    joinDate: 'March 2022',
    avatar: null
  });

  const [settings, setSettings] = useState({
    emailNotifications: true,
    pushNotifications: false,
    twoFactorAuth: false
  });

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save to your backend
  };

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSettingChange = (setting, value) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  if (authLoading || !user) {
    return <div className="min-h-screen flex items-center justify-center">Checking authentication...</div>;
  }

  // Determine if user is OAuth
  const isOAuthUser = user.identities && user.identities.length > 0 && user.identities[0].provider !== 'email';

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header - Now Static */}
      <div className="border-b bg-white/80 backdrop-blur-sm border-slate-200/50">
        <div className="max-w-4xl px-6 py-4 mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-transparent bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text">
                Profile Settings
              </h1>
              <p className="mt-1 text-slate-600">Manage your account and preferences</p>
              <div className="mt-2 text-gray-700 text-sm">
                <span className="font-semibold">{user.email}</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {isEditing ? (
                <>
                  <button
                    onClick={() => setIsEditing(false)}
                    className="flex items-center gap-2 px-4 py-2 transition-all duration-200 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100"
                  >
                    <X size={16} />
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    className="flex items-center gap-2 px-4 py-2 text-white transition-all duration-200 rounded-lg shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-blue-500/25"
                  >
                    <Save size={16} />
                    Save Changes
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 px-4 py-2 text-white transition-all duration-200 rounded-lg shadow-lg bg-gradient-to-r from-slate-800 to-slate-600 hover:from-slate-900 hover:to-slate-700 shadow-slate-500/25"
                >
                  <Edit3 size={16} />
                  Edit Profile
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl px-6 py-8 mx-auto">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="p-6 border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50">
              <div className="flex flex-col items-center text-center">
                <div className="relative group">
                  <div className="flex items-center justify-center w-20 h-20 text-2xl font-bold text-white rounded-full shadow-lg bg-gradient-to-br from-blue-500 to-indigo-600">
                    {profileData.avatar ? (
                      <img src={profileData.avatar} alt="Profile" className="object-cover w-full h-full rounded-full" />
                    ) : (
                      profileData.name.split(' ').map(n => n[0]).join('')
                    )}
                  </div>
                  {isEditing && (
                    <button className="absolute flex items-center justify-center w-8 h-8 transition-colors bg-white border-2 border-blue-500 rounded-full shadow-lg -bottom-2 -right-2 hover:bg-blue-50">
                      <Camera size={14} className="text-blue-600" />
                    </button>
                  )}
                </div>
                <h2 className="mt-3 text-lg font-semibold text-slate-800">{profileData.name}</h2>
                <p className="text-sm text-slate-600">{profileData.email}</p>
              </div>
            </div>
          </div>

          {/* Main Content - All Sections Combined */}
          <div className="lg:col-span-3">
            <div className="overflow-hidden border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50">
              
              {/* Personal Information Section */}
              <div className="p-8 border-b border-slate-200/50">
                <div className="mb-8">
                  <h3 className="mb-2 text-xl font-semibold text-slate-800">Personal Information</h3>
                  <p className="text-slate-600">Update your personal details and profile information</p>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Full Name</label>
                      <div className="relative">
                        <User className="absolute w-5 h-5 left-3 top-3 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Email Address</label>
                      <div className="relative">
                        <Mail className="absolute w-5 h-5 left-3 top-3 text-slate-400" />
                        <input
                          type="email"
                          value={profileData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Phone Number</label>
                      <div className="relative">
                        <Phone className="absolute w-5 h-5 left-3 top-3 text-slate-400" />
                        <input
                          type="tel"
                          value={profileData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block mb-2 text-sm font-medium text-slate-700">Location</label>
                      <div className="relative">
                        <MapPin className="absolute w-5 h-5 left-3 top-3 text-slate-400" />
                        <input
                          type="text"
                          value={profileData.location}
                          onChange={(e) => handleInputChange('location', e.target.value)}
                          disabled={!isEditing}
                          className="w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 p-4 text-sm text-slate-600 bg-slate-50/50 rounded-xl">
                    <Calendar size={16} />
                    <span>Member since {profileData.joinDate}</span>
                  </div>
                </div>
              </div>

              {/* Security Settings Section */}
              <div className="p-8 border-b border-slate-200/50">
                <div className="mb-8">
                  <h3 className="mb-2 text-xl font-semibold text-slate-800">Security Settings</h3>
                  <p className="text-slate-600">Manage your account security and authentication</p>
                </div>

                <div className="space-y-6">
                  <div className="p-6 border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-slate-800">Two-Factor Authentication</h4>
                        <p className="mt-1 text-sm text-slate-600">Add an extra layer of security to your account</p>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.twoFactorAuth}
                          onChange={(e) => handleSettingChange('twoFactorAuth', e.target.checked)}
                          className="sr-only"
                        />
                        <button
                          onClick={() => handleSettingChange('twoFactorAuth', !settings.twoFactorAuth)}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            settings.twoFactorAuth ? 'bg-blue-600' : 'bg-slate-300'
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Only show Change Password if not OAuth user */}
                  {!isOAuthUser && (
                    <div className="p-6 border bg-white/50 rounded-xl border-slate-200">
                      <h4 className="mb-4 font-semibold text-slate-800">Change Password</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block mb-2 text-sm font-medium text-slate-700">Current Password</label>
                          <div className="relative">
                            <Lock className="absolute w-5 h-5 left-3 top-3 text-slate-400" />
                            <input
                              type="password"
                              className="w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter current password"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block mb-2 text-sm font-medium text-slate-700">New Password</label>
                          <div className="relative">
                            <Lock className="absolute w-5 h-5 left-3 top-3 text-slate-400" />
                            <input
                              type="password"
                              className="w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter new password"
                            />
                          </div>
                        </div>
                        <button className="px-4 py-2 text-white transition-all duration-200 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
                          Update Password
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Notification Settings Section */}
              <div className="p-8 border-b border-slate-200/50">
                <div className="mb-8">
                  <h3 className="mb-2 text-xl font-semibold text-slate-800">Notification Preferences</h3>
                  <p className="text-slate-600">Choose how you want to be notified</p>
                </div>

                <div className="space-y-6">
                  <div className="p-6 border bg-white/50 rounded-xl border-slate-200">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-semibold text-slate-800">Email Notifications</h4>
                        <p className="mt-1 text-sm text-slate-600">Receive notifications via email</p>
                      </div>
                      <button
                        onClick={() => handleSettingChange('emailNotifications', !settings.emailNotifications)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          settings.emailNotifications ? 'bg-blue-600' : 'bg-slate-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            settings.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>

                  <div className="p-6 border bg-white/50 rounded-xl border-slate-200">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-semibold text-slate-800">Push Notifications</h4>
                        <p className="mt-1 text-sm text-slate-600">Receive push notifications on your device</p>
                      </div>
                      <button
                        onClick={() => handleSettingChange('pushNotifications', !settings.pushNotifications)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          settings.pushNotifications ? 'bg-blue-600' : 'bg-slate-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            settings.pushNotifications ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Account Management Section */}
              <div className="p-8">
                <div className="mb-8">
                  <h3 className="mb-2 text-xl font-semibold text-slate-800">Account Management</h3>
                  <p className="text-slate-600">Manage your account settings</p>
                </div>

                <div className="p-6 border border-red-200 bg-red-50 rounded-xl">
                  <h4 className="mb-2 font-semibold text-red-800">Delete Account</h4>
                  <p className="mb-4 text-sm text-red-600">Permanently delete your account and all associated data</p>
                  <button className="px-4 py-2 text-white transition-colors bg-red-600 rounded-lg hover:bg-red-700">
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}