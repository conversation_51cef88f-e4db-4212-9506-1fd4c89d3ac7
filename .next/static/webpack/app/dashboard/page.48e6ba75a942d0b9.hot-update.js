"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _MapDomainModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MapDomainModal */ \"(app-pages-browser)/./src/app/dashboard/MapDomainModal.tsx\");\n/* harmony import */ var _AskDomainModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AskDomainModal */ \"(app-pages-browser)/./src/app/dashboard/AskDomainModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/providers/AuthProvider */ \"(app-pages-browser)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DashboardPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { user, loading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sitesLoading, setSitesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMapDomainOpen, setIsMapDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAskDomainOpen, setIsAskDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSiteName, setSelectedSiteName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user) {\n            router.replace(\"/login\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Trigger AskDomainModal if redirected from checkout\n        const postCheckout = searchParams.get(\"postCheckout\");\n        const siteIdFromParam = searchParams.get(\"siteId\");\n        if (postCheckout && siteIdFromParam) {\n            setSelectedSiteId(siteIdFromParam);\n            setIsAskDomainOpen(true);\n        }\n    }, [\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-websites\").select(\"id, site_name, expiry_status, expiry_time\"); // Fetch expiry_time\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n                setError(err instanceof Error ? err.message : \"An unknown error occurred\");\n            } finally{\n                setSitesLoading(false);\n            }\n        };\n        fetchSites();\n    }, []);\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Checking authentication...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 70,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (sitesLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 74,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center text-red-500\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 78,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col p-4 mb-4 space-y-4 bg-white rounded-lg shadow-md sm:flex-row sm:items-center sm:justify-between sm:p-6 sm:mb-6 sm:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-800 sm:text-2xl\",\n                        children: \"Websites\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full sm:w-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search websites...\",\n                                    className: \"w-full py-2 pl-10 pr-4 text-sm border rounded-md sm:w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:text-base\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 sm:w-5 sm:h-5 left-3 top-1/2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 hidden overflow-hidden bg-white rounded-lg shadow-md md:block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            scope: \"col\",\n                                            className: \"px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6\",\n                                            children: \"Site Name\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            scope: \"col\",\n                                            className: \"px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            scope: \"col\",\n                                            className: \"px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6\",\n                                            children: \"Expiry\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"flex items-center px-4 py-4 text-sm font-medium text-gray-900 lg:px-6 whitespace-nowrap\",\n                                                children: [\n                                                    site.site_name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-4 text-sm font-medium text-right lg:px-6 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-blue-500 hover:text-blue-700\",\n                                                            title: \"Map Domain\",\n                                                            onClick: ()=>{\n                                                                setSelectedSiteName(site.site_name);\n                                                                setIsMapDomainOpen(true);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"w-4 h-4 lg:w-5 lg:h-5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-green-500 hover:text-green-700\",\n                                                            title: \"Choose Plan\",\n                                                            onClick: ()=>{\n                                                                window.location.href = \"/payments?siteId=\".concat(site.id);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"w-4 h-4 lg:w-5 lg:h-5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-purple-500 hover:text-purple-700\",\n                                                            title: \"Change Plan\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"w-4 h-4 lg:w-5 lg:h-5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                        x: \"2\",\n                                                                        y: \"7\",\n                                                                        width: \"20\",\n                                                                        height: \"10\",\n                                                                        rx: \"2\",\n                                                                        ry: \"2\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        fill: \"none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M2 11h20\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        cx: \"7\",\n                                                                        cy: \"15\",\n                                                                        r: \"1\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 182,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        cx: \"11\",\n                                                                        cy: \"15\",\n                                                                        r: \"1\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-4 text-sm lg:px-6 whitespace-nowrap\",\n                                                children: site.expiry_status === \"Temporary\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                    title: (()=>{\n                                                        if (!site.expiry_time) return \"No expiry time set\";\n                                                        const expiry = new Date(site.expiry_time);\n                                                        const now = new Date();\n                                                        const diffMs = expiry.getTime() - now.getTime();\n                                                        if (diffMs <= 0) return \"Expired\";\n                                                        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n                                                        const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n                                                        return \"\".concat(diffDays, \" days \").concat(diffHours, \" hours left (expires at \").concat(expiry.toLocaleString(), \")\");\n                                                    })(),\n                                                    children: site.expiry_status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\",\n                                                    children: site.expiry_status\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, site.id, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg\",\n                        children: [\n                            sites.length,\n                            \" Sites\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-4 md:hidden\",\n                children: [\n                    (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white rounded-lg shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: site.site_name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        site.expiry_status === \"Temporary\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                            title: (()=>{\n                                                if (!site.expiry_time) return \"No expiry time set\";\n                                                const expiry = new Date(site.expiry_time);\n                                                const now = new Date();\n                                                const diffMs = expiry.getTime() - now.getTime();\n                                                if (diffMs <= 0) return \"Expired\";\n                                                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n                                                const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n                                                return \"\".concat(diffDays, \" days \").concat(diffHours, \" hours left (expires at \").concat(expiry.toLocaleString(), \")\");\n                                            })(),\n                                            children: site.expiry_status\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\",\n                                            children: site.expiry_status\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-start pt-3 space-x-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center px-3 py-2 space-x-2 text-blue-500 transition-colors rounded-md hover:text-blue-700 hover:bg-blue-50\",\n                                            onClick: ()=>{\n                                                setSelectedSiteName(site.site_name);\n                                                setIsMapDomainOpen(true);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Map Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center px-3 py-2 space-x-2 text-green-500 transition-colors rounded-md hover:text-green-700 hover:bg-green-50\",\n                                            onClick: ()=>{\n                                                window.location.href = \"/payments?siteId=\".concat(site.id);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Go Live\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center px-3 py-2 space-x-2 text-purple-500 transition-colors rounded-md hover:text-purple-700 hover:bg-purple-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                            x: \"2\",\n                                                            y: \"7\",\n                                                            width: \"20\",\n                                                            height: \"10\",\n                                                            rx: \"2\",\n                                                            ry: \"2\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            fill: \"none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M2 11h20\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"7\",\n                                                            cy: \"15\",\n                                                            r: \"1\",\n                                                            fill: \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"11\",\n                                                            cy: \"15\",\n                                                            r: \"1\",\n                                                            fill: \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Change Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, site.id, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 text-sm text-center text-gray-600 bg-white rounded-lg shadow-md\",\n                        children: [\n                            sites.length,\n                            \" Sites\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MapDomainModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isMapDomainOpen,\n                onClose: ()=>setIsMapDomainOpen(false),\n                siteName: selectedSiteName\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AskDomainModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isAskDomainOpen,\n                onYes: ()=>{\n                    // open map domain modal\n                    const site = sites.find((s)=>s.id === selectedSiteId);\n                    if (site) {\n                        setSelectedSiteName(site.site_name);\n                        setIsMapDomainOpen(true);\n                    }\n                    setIsAskDomainOpen(false);\n                },\n                onNo: ()=>{\n                    router.push(\"/dashboard/domain\");\n                    setIsAskDomainOpen(false);\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DashboardPage, \"934wzBn8lbkM07rSgBYJpHaWHNM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = DashboardPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DashboardPage);\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUNtRDtBQUN5QjtBQUM5QjtBQUNBO0FBQ2U7QUFDSztBQVNsRSxNQUFNUyxnQkFBMEI7O0lBQzlCLE1BQU1DLGVBQWVILGdFQUFlQTtJQUNwQyxNQUFNSSxTQUFTTCwwREFBU0E7SUFDeEIsTUFBTSxFQUFFTSxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHTCwyRUFBT0E7SUFDakMsTUFBTSxDQUFDTSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFTLEVBQUU7SUFDN0MsTUFBTSxDQUFDYyxjQUFjQyxnQkFBZ0IsR0FBR2YsK0NBQVFBLENBQVU7SUFDMUQsTUFBTSxDQUFDZ0IsT0FBT0MsU0FBUyxHQUFHakIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ2tCLFFBQVFDLFVBQVUsR0FBR25CLCtDQUFRQSxDQUFTO0lBQzdDLE1BQU0sQ0FBQ29CLGlCQUFpQkMsbUJBQW1CLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNzQixpQkFBaUJDLG1CQUFtQixHQUFHdkIsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDd0Isa0JBQWtCQyxvQkFBb0IsR0FBR3pCLCtDQUFRQSxDQUFTO0lBQ2pFLE1BQU0sQ0FBQzBCLGdCQUFnQkMsa0JBQWtCLEdBQUczQiwrQ0FBUUEsQ0FBUztJQUM3RCxNQUFNNEIsV0FBVzNCLDBGQUEyQkE7SUFFNUNGLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDWSxXQUFXLENBQUNELE1BQU07WUFDckJELE9BQU9vQixPQUFPLENBQUM7UUFDakI7SUFDRixHQUFHO1FBQUNuQjtRQUFNQztRQUFTRjtLQUFPO0lBRTFCVixnREFBU0EsQ0FBQztRQUNSLHFEQUFxRDtRQUNyRCxNQUFNK0IsZUFBZXRCLGFBQWF1QixHQUFHLENBQUM7UUFDdEMsTUFBTUMsa0JBQWtCeEIsYUFBYXVCLEdBQUcsQ0FBQztRQUN6QyxJQUFJRCxnQkFBZ0JFLGlCQUFpQjtZQUNuQ0wsa0JBQWtCSztZQUNsQlQsbUJBQW1CO1FBQ3JCO0lBQ0YsR0FBRztRQUFDZjtLQUFhO0lBRWpCVCxnREFBU0EsQ0FBQztRQUNSLE1BQU1rQyxhQUFhO1lBQ2pCLElBQUk7Z0JBQ0YsTUFBTSxFQUFFQyxJQUFJLEVBQUVsQixLQUFLLEVBQUUsR0FBRyxNQUFNWSxTQUMzQk8sSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsOENBQThDLG9CQUFvQjtnQkFFNUUsSUFBSXBCLE9BQU87b0JBQ1QsTUFBTUE7Z0JBQ1I7Z0JBRUFILFNBQVNxQjtZQUNYLEVBQUUsT0FBT0csS0FBSztnQkFDWkMsUUFBUXRCLEtBQUssQ0FBQyx5QkFBeUJxQjtnQkFDdkNwQixTQUFTb0IsZUFBZUUsUUFBUUYsSUFBSUcsT0FBTyxHQUFHO1lBQ2hELFNBQVU7Z0JBQ1J6QixnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBa0I7SUFDRixHQUFHLEVBQUU7SUFFTCxJQUFJdEIsV0FBVyxDQUFDRCxNQUFNO1FBQ3BCLHFCQUFPLDhEQUFDK0I7WUFBSUMsV0FBVTtzQkFBbUI7Ozs7OztJQUMzQztJQUVBLElBQUk1QixjQUFjO1FBQ2hCLHFCQUFPLDhEQUFDMkI7WUFBSUMsV0FBVTtzQkFBbUI7Ozs7OztJQUMzQztJQUVBLElBQUkxQixPQUFPO1FBQ1QscUJBQU8sOERBQUN5QjtZQUFJQyxXQUFVOztnQkFBZ0M7Z0JBQVExQjs7Ozs7OztJQUNoRTtJQUVBLHFCQUNFLDhEQUFDeUI7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQWtEOzs7Ozs7a0NBQ2hFLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FDQ0MsTUFBSztvQ0FDTEMsYUFBWTtvQ0FDWkosV0FBVTtvQ0FDVkssT0FBTzdCO29DQUNQOEIsVUFBVUMsQ0FBQUEsSUFBSzlCLFVBQVU4QixFQUFFQyxNQUFNLENBQUNILEtBQUs7Ozs7Ozs4Q0FFekMsOERBQUNJO29DQUNDVCxXQUFVO29DQUNWVSxNQUFLO29DQUNMQyxRQUFPO29DQUNQQyxTQUFRO29DQUNSQyxPQUFNOzhDQUVOLDRFQUFDQzt3Q0FDQ0MsZUFBYzt3Q0FDZEMsZ0JBQWU7d0NBQ2ZDLGFBQVk7d0NBQ1pDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVosOERBQUNuQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNtQjt3QkFBTW5CLFdBQVU7OzBDQUNmLDhEQUFDb0I7Z0NBQU1wQixXQUFVOzBDQUNmLDRFQUFDcUI7O3NEQUNDLDhEQUFDQzs0Q0FDQ0MsT0FBTTs0Q0FDTnZCLFdBQVU7c0RBQ1g7Ozs7OztzREFHRCw4REFBQ3NCOzRDQUNDQyxPQUFNOzRDQUNOdkIsV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDc0I7NENBQ0NDLE9BQU07NENBQ052QixXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLTCw4REFBQ3dCO2dDQUFNeEIsV0FBVTswQ0FDZCxDQUFDeEIsT0FBT2lELElBQUksS0FBS3ZELE1BQU13RCxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLFNBQVMsQ0FBQ0MsV0FBVyxHQUFHQyxRQUFRLENBQUN0RCxPQUFPcUQsV0FBVyxPQUFPM0QsS0FBSSxFQUFHNkQsR0FBRyxDQUFDLENBQUNKLHFCQUNoSCw4REFBQ047OzBEQUNDLDhEQUFDVztnREFBR2hDLFdBQVU7O29EQUNYMkIsS0FBS0MsU0FBUztrRUFDZiw4REFBQ0s7d0RBQUVDLE1BQUs7d0RBQUlsQyxXQUFVO2tFQUNwQiw0RUFBQ1M7NERBQUlJLE9BQU07NERBQTZCYixXQUFVOzREQUFVVSxNQUFLOzREQUFPRSxTQUFROzREQUFZRCxRQUFPO3NFQUNqRyw0RUFBQ0c7Z0VBQUtDLGVBQWM7Z0VBQVFDLGdCQUFlO2dFQUFRQyxhQUFhO2dFQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUkzRSw4REFBQ2M7Z0RBQUdoQyxXQUFVOzBEQUNaLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBRWIsOERBQUNtQzs0REFDQ25DLFdBQVU7NERBQ1ZvQyxPQUFNOzREQUNOQyxTQUFTO2dFQUFRdEQsb0JBQW9CNEMsS0FBS0MsU0FBUztnRUFBR2pELG1CQUFtQjs0REFBTztzRUFFaEYsNEVBQUM4QjtnRUFBSUksT0FBTTtnRUFBNkJiLFdBQVU7Z0VBQXdCVSxNQUFLO2dFQUFPRSxTQUFRO2dFQUFZRCxRQUFPOzBFQUMvRyw0RUFBQ0c7b0VBQUtDLGVBQWM7b0VBQVFDLGdCQUFlO29FQUFRQyxhQUFhO29FQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dEQUl4RVMsS0FBS1csYUFBYSxLQUFLLDZCQUN0Qiw4REFBQ0g7NERBQ0NuQyxXQUFVOzREQUNWb0MsT0FBTTs0REFDTkMsU0FBUztnRUFBUUUsT0FBT0MsUUFBUSxDQUFDTixJQUFJLEdBQUcsb0JBQTRCLE9BQVJQLEtBQUtjLEVBQUU7NERBQUc7c0VBRXRFLDRFQUFDaEM7Z0VBQUlJLE9BQU07Z0VBQTZCYixXQUFVO2dFQUF3QlUsTUFBSztnRUFBT0UsU0FBUTtnRUFBWUQsUUFBTzswRUFDL0csNEVBQUNHO29FQUFLQyxlQUFjO29FQUFRQyxnQkFBZTtvRUFBUUMsYUFBYTtvRUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozt3REFLMUVTLEtBQUtXLGFBQWEsS0FBSyw2QkFDdEIsOERBQUNIOzREQUNDbkMsV0FBVTs0REFDVm9DLE9BQU07c0VBRU4sNEVBQUMzQjtnRUFBSUksT0FBTTtnRUFBNkJiLFdBQVU7Z0VBQXdCVSxNQUFLO2dFQUFPRSxTQUFRO2dFQUFZRCxRQUFPOztrRkFDL0csOERBQUMrQjt3RUFBS0MsR0FBRTt3RUFBSUMsR0FBRTt3RUFBSUMsT0FBTTt3RUFBS0MsUUFBTzt3RUFBS0MsSUFBRzt3RUFBSUMsSUFBRzt3RUFBSXJDLFFBQU87d0VBQWVNLGFBQVk7d0VBQUlQLE1BQUs7Ozs7OztrRkFDbEcsOERBQUNJO3dFQUFLSSxHQUFFO3dFQUFXUCxRQUFPO3dFQUFlTSxhQUFZOzs7Ozs7a0ZBQ3JELDhEQUFDZ0M7d0VBQU9DLElBQUc7d0VBQUlDLElBQUc7d0VBQUtDLEdBQUU7d0VBQUkxQyxNQUFLOzs7Ozs7a0ZBQ2xDLDhEQUFDdUM7d0VBQU9DLElBQUc7d0VBQUtDLElBQUc7d0VBQUtDLEdBQUU7d0VBQUkxQyxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU03Qyw4REFBQ3NCO2dEQUFHaEMsV0FBVTswREFFWDJCLEtBQUtXLGFBQWEsS0FBSyw0QkFDdEIsOERBQUNlO29EQUNDckQsV0FBWTtvREFDWm9DLE9BQU8sQ0FBQzt3REFDTixJQUFJLENBQUNULEtBQUsyQixXQUFXLEVBQUUsT0FBTzt3REFDOUIsTUFBTUMsU0FBUyxJQUFJQyxLQUFLN0IsS0FBSzJCLFdBQVc7d0RBQ3hDLE1BQU1HLE1BQU0sSUFBSUQ7d0RBQ2hCLE1BQU1FLFNBQVNILE9BQU9JLE9BQU8sS0FBS0YsSUFBSUUsT0FBTzt3REFDN0MsSUFBSUQsVUFBVSxHQUFHLE9BQU87d0RBQ3hCLE1BQU1FLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0osU0FBVSxRQUFPLEtBQUssS0FBSyxFQUFDO3dEQUN4RCxNQUFNSyxZQUFZRixLQUFLQyxLQUFLLENBQUMsU0FBVyxRQUFPLEtBQUssS0FBSyxFQUFDLElBQU8sUUFBTyxLQUFLLEVBQUM7d0RBQzlFLE9BQU8sR0FBb0JDLE9BQWpCSCxVQUFTLFVBQTRDTCxPQUFwQ1EsV0FBVSw0QkFBa0QsT0FBeEJSLE9BQU9TLGNBQWMsSUFBRztvREFDekY7OERBRUNyQyxLQUFLVyxhQUFhOzs7Ozs4RUFHckIsOERBQUNlO29EQUNDckQsV0FBWTs4REFFWDJCLEtBQUtXLGFBQWE7Ozs7Ozs7Ozs7Ozt1Q0F2RWxCWCxLQUFLYyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tDQWdGdEIsOERBQUMxQzt3QkFBSUMsV0FBVTs7NEJBQ1o5QixNQUFNK0YsTUFBTTs0QkFBQzs7Ozs7Ozs7Ozs7OzswQkFLbEIsOERBQUNsRTtnQkFBSUMsV0FBVTs7b0JBQ1h4QixDQUFBQSxPQUFPaUQsSUFBSSxLQUFLdkQsTUFBTXdELE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsU0FBUyxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3RELE9BQU9xRCxXQUFXLE9BQU8zRCxLQUFJLEVBQUc2RCxHQUFHLENBQUMsQ0FBQ0oscUJBQ2hILDhEQUFDNUI7NEJBQWtCQyxXQUFVOzs4Q0FDM0IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDa0U7b0RBQUdsRSxXQUFVOzhEQUFxQzJCLEtBQUtDLFNBQVM7Ozs7Ozs4REFDakUsOERBQUNLO29EQUFFQyxNQUFLO29EQUFJbEMsV0FBVTs4REFDcEIsNEVBQUNTO3dEQUFJSSxPQUFNO3dEQUE2QmIsV0FBVTt3REFBVVUsTUFBSzt3REFBT0UsU0FBUTt3REFBWUQsUUFBTztrRUFDakcsNEVBQUNHOzREQUFLQyxlQUFjOzREQUFRQyxnQkFBZTs0REFBUUMsYUFBYTs0REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FLMUVTLEtBQUtXLGFBQWEsS0FBSyw0QkFDdEIsOERBQUNlOzRDQUNDckQsV0FBWTs0Q0FDWm9DLE9BQU8sQ0FBQztnREFDTixJQUFJLENBQUNULEtBQUsyQixXQUFXLEVBQUUsT0FBTztnREFDOUIsTUFBTUMsU0FBUyxJQUFJQyxLQUFLN0IsS0FBSzJCLFdBQVc7Z0RBQ3hDLE1BQU1HLE1BQU0sSUFBSUQ7Z0RBQ2hCLE1BQU1FLFNBQVNILE9BQU9JLE9BQU8sS0FBS0YsSUFBSUUsT0FBTztnREFDN0MsSUFBSUQsVUFBVSxHQUFHLE9BQU87Z0RBQ3hCLE1BQU1FLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0osU0FBVSxRQUFPLEtBQUssS0FBSyxFQUFDO2dEQUN4RCxNQUFNSyxZQUFZRixLQUFLQyxLQUFLLENBQUMsU0FBVyxRQUFPLEtBQUssS0FBSyxFQUFDLElBQU8sUUFBTyxLQUFLLEVBQUM7Z0RBQzlFLE9BQU8sR0FBb0JDLE9BQWpCSCxVQUFTLFVBQTRDTCxPQUFwQ1EsV0FBVSw0QkFBa0QsT0FBeEJSLE9BQU9TLGNBQWMsSUFBRzs0Q0FDekY7c0RBRUNyQyxLQUFLVyxhQUFhOzs7OztzRUFHckIsOERBQUNlOzRDQUNDckQsV0FBWTtzREFFWDJCLEtBQUtXLGFBQWE7Ozs7Ozs7Ozs7Ozs4Q0FNekIsOERBQUN2QztvQ0FBSUMsV0FBVTs7c0RBRWIsOERBQUNtQzs0Q0FDQ25DLFdBQVU7NENBQ1ZxQyxTQUFTO2dEQUFRdEQsb0JBQW9CNEMsS0FBS0MsU0FBUztnREFBR2pELG1CQUFtQjs0Q0FBTzs7OERBRWhGLDhEQUFDOEI7b0RBQUlJLE9BQU07b0RBQTZCYixXQUFVO29EQUFVVSxNQUFLO29EQUFPRSxTQUFRO29EQUFZRCxRQUFPOzhEQUNqRyw0RUFBQ0c7d0RBQUtDLGVBQWM7d0RBQVFDLGdCQUFlO3dEQUFRQyxhQUFhO3dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs4REFFdkUsOERBQUNtQztvREFBS3JELFdBQVU7OERBQXNCOzs7Ozs7Ozs7Ozs7d0NBSXZDMkIsS0FBS1csYUFBYSxLQUFLLDZCQUN0Qiw4REFBQ0g7NENBQ0NuQyxXQUFVOzRDQUNWcUMsU0FBUztnREFBUUUsT0FBT0MsUUFBUSxDQUFDTixJQUFJLEdBQUcsb0JBQTRCLE9BQVJQLEtBQUtjLEVBQUU7NENBQUc7OzhEQUV0RSw4REFBQ2hDO29EQUFJSSxPQUFNO29EQUE2QmIsV0FBVTtvREFBVVUsTUFBSztvREFBT0UsU0FBUTtvREFBWUQsUUFBTzs4REFDakcsNEVBQUNHO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7OERBRXZFLDhEQUFDbUM7b0RBQUtyRCxXQUFVOzhEQUFzQjs7Ozs7Ozs7Ozs7O3dDQUt6QzJCLEtBQUtXLGFBQWEsS0FBSyw2QkFDdEIsOERBQUNIOzRDQUNDbkMsV0FBVTs7OERBRVYsOERBQUNTO29EQUFJSSxPQUFNO29EQUE2QmIsV0FBVTtvREFBVVUsTUFBSztvREFBT0UsU0FBUTtvREFBWUQsUUFBTzs7c0VBQ2pHLDhEQUFDK0I7NERBQUtDLEdBQUU7NERBQUlDLEdBQUU7NERBQUlDLE9BQU07NERBQUtDLFFBQU87NERBQUtDLElBQUc7NERBQUlDLElBQUc7NERBQUlyQyxRQUFPOzREQUFlTSxhQUFZOzREQUFJUCxNQUFLOzs7Ozs7c0VBQ2xHLDhEQUFDSTs0REFBS0ksR0FBRTs0REFBV1AsUUFBTzs0REFBZU0sYUFBWTs7Ozs7O3NFQUNyRCw4REFBQ2dDOzREQUFPQyxJQUFHOzREQUFJQyxJQUFHOzREQUFLQyxHQUFFOzREQUFJMUMsTUFBSzs7Ozs7O3NFQUNsQyw4REFBQ3VDOzREQUFPQyxJQUFHOzREQUFLQyxJQUFHOzREQUFLQyxHQUFFOzREQUFJMUMsTUFBSzs7Ozs7Ozs7Ozs7OzhEQUVyQyw4REFBQzJDO29EQUFLckQsV0FBVTs4REFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBekVwQzJCLEtBQUtjLEVBQUU7Ozs7O2tDQWlGbkIsOERBQUMxQzt3QkFBSUMsV0FBVTs7NEJBQ1o5QixNQUFNK0YsTUFBTTs0QkFBQzs7Ozs7Ozs7Ozs7OzswQkFHbEIsOERBQUN6Ryx1REFBY0E7Z0JBQ2IyRyxRQUFRekY7Z0JBQ1IwRixTQUFTLElBQU16RixtQkFBbUI7Z0JBQ2xDMEYsVUFBVXZGOzs7Ozs7MEJBR1osOERBQUNyQix1REFBY0E7Z0JBQ2IwRyxRQUFRdkY7Z0JBQ1IwRixPQUFPO29CQUNMLHdCQUF3QjtvQkFDeEIsTUFBTTNDLE9BQU96RCxNQUFNcUcsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFL0IsRUFBRSxLQUFLekQ7b0JBQ3RDLElBQUkyQyxNQUFNO3dCQUNSNUMsb0JBQW9CNEMsS0FBS0MsU0FBUzt3QkFDbENqRCxtQkFBbUI7b0JBQ3JCO29CQUNBRSxtQkFBbUI7Z0JBQ3JCO2dCQUNBNEYsTUFBTTtvQkFDSjFHLE9BQU8yRyxJQUFJLENBQUU7b0JBQ2I3RixtQkFBbUI7Z0JBQ3JCOzs7Ozs7Ozs7Ozs7QUFJUjtHQWpVTWhCOztRQUNpQkYsNERBQWVBO1FBQ3JCRCxzREFBU0E7UUFDRUUsdUVBQU9BOzs7S0FIN0JDO0FBbVVOLCtEQUFlQSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4P2MxNWUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9hdXRoLWhlbHBlcnMtbmV4dGpzJztcbmltcG9ydCBNYXBEb21haW5Nb2RhbCBmcm9tICcuL01hcERvbWFpbk1vZGFsJztcbmltcG9ydCBBc2tEb21haW5Nb2RhbCBmcm9tICcuL0Fza0RvbWFpbk1vZGFsJztcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL3Byb3ZpZGVycy9BdXRoUHJvdmlkZXInO1xuXG5pbnRlcmZhY2UgU2l0ZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHNpdGVfbmFtZTogc3RyaW5nO1xuICBleHBpcnlfc3RhdHVzOiAnUGVybWFuZW50JyB8ICdUZW1wb3JhcnknO1xuICBleHBpcnlfdGltZT86IHN0cmluZzsgLy8gQWRkIGV4cGlyeV90aW1lIGFzIG9wdGlvbmFsXG59XG5cbmNvbnN0IERhc2hib2FyZFBhZ2U6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHsgdXNlciwgbG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbc2l0ZXMsIHNldFNpdGVzXSA9IHVzZVN0YXRlPFNpdGVbXT4oW10pO1xuICBjb25zdCBbc2l0ZXNMb2FkaW5nLCBzZXRTaXRlc0xvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4odHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWFyY2gsIHNldFNlYXJjaF0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xuICBjb25zdCBbaXNNYXBEb21haW5PcGVuLCBzZXRJc01hcERvbWFpbk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNBc2tEb21haW5PcGVuLCBzZXRJc0Fza0RvbWFpbk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VsZWN0ZWRTaXRlTmFtZSwgc2V0U2VsZWN0ZWRTaXRlTmFtZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkU2l0ZUlkLCBzZXRTZWxlY3RlZFNpdGVJZF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbG9hZGluZyAmJiAhdXNlcikge1xuICAgICAgcm91dGVyLnJlcGxhY2UoJy9sb2dpbicpO1xuICAgIH1cbiAgfSwgW3VzZXIsIGxvYWRpbmcsIHJvdXRlcl0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gVHJpZ2dlciBBc2tEb21haW5Nb2RhbCBpZiByZWRpcmVjdGVkIGZyb20gY2hlY2tvdXRcbiAgICBjb25zdCBwb3N0Q2hlY2tvdXQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdwb3N0Q2hlY2tvdXQnKTtcbiAgICBjb25zdCBzaXRlSWRGcm9tUGFyYW0gPSBzZWFyY2hQYXJhbXMuZ2V0KCdzaXRlSWQnKTtcbiAgICBpZiAocG9zdENoZWNrb3V0ICYmIHNpdGVJZEZyb21QYXJhbSkge1xuICAgICAgc2V0U2VsZWN0ZWRTaXRlSWQoc2l0ZUlkRnJvbVBhcmFtKTtcbiAgICAgIHNldElzQXNrRG9tYWluT3Blbih0cnVlKTtcbiAgICB9XG4gIH0sIFtzZWFyY2hQYXJhbXNdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoU2l0ZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCd1c2VyLXdlYnNpdGVzJylcbiAgICAgICAgICAuc2VsZWN0KCdpZCwgc2l0ZV9uYW1lLCBleHBpcnlfc3RhdHVzLCBleHBpcnlfdGltZScpOyAvLyBGZXRjaCBleHBpcnlfdGltZVxuXG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG5cbiAgICAgICAgc2V0U2l0ZXMoZGF0YSBhcyBTaXRlW10pO1xuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHNpdGVzOicsIGVycik7XG4gICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gdW5rbm93biBlcnJvciBvY2N1cnJlZCcpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0U2l0ZXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZmV0Y2hTaXRlcygpO1xuICB9LCBbXSk7XG5cbiAgaWYgKGxvYWRpbmcgfHwgIXVzZXIpIHtcbiAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJweS04IHRleHQtY2VudGVyXCI+Q2hlY2tpbmcgYXV0aGVudGljYXRpb24uLi48L2Rpdj47XG4gIH1cblxuICBpZiAoc2l0ZXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIDxkaXYgY2xhc3NOYW1lPVwicHktOCB0ZXh0LWNlbnRlclwiPkxvYWRpbmcgc2l0ZXMuLi48L2Rpdj47XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJweS04IHRleHQtY2VudGVyIHRleHQtcmVkLTUwMFwiPkVycm9yOiB7ZXJyb3J9PC9kaXY+O1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIGJnLWdyYXktMTAwXCI+XG4gICAgICB7LyogRGFzaGJvYXJkIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBwLTQgbWItNCBzcGFjZS15LTQgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWQgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlbiBzbTpwLTYgc206bWItNiBzbTpzcGFjZS15LTBcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIHNtOnRleHQtMnhsXCI+V2Vic2l0ZXM8L2gxPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIHNtOnctYXV0b1wiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggd2Vic2l0ZXMuLi5cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHktMiBwbC0xMCBwci00IHRleHQtc20gYm9yZGVyIHJvdW5kZWQtbWQgc206dy02NCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgc206dGV4dC1iYXNlXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0U2VhcmNoKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHctNCBoLTQgdGV4dC1ncmF5LTQwMCB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBzbTp3LTUgc206aC01IGxlZnQtMyB0b3AtMS8yXCJcbiAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxuICAgICAgICAgICAgICAgIGQ9XCJNMjEgMjFsLTYtNm0yLTVhNyA3IDAgMTEtMTQgMCA3IDcgMCAwMTE0IDB6XCJcbiAgICAgICAgICAgICAgPjwvcGF0aD5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU2l0ZXMgVGFibGUgLSBEZXNrdG9wICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgaGlkZGVuIG92ZXJmbG93LWhpZGRlbiBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBtZDpibG9ja1wiPlxuICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICA8dGhcbiAgICAgICAgICAgICAgICBzY29wZT1cImNvbFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQteHMgZm9udC1tZWRpdW0gdHJhY2tpbmctd2lkZXIgdGV4dC1sZWZ0IHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIGxnOnB4LTZcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU2l0ZSBOYW1lXG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgIDx0aFxuICAgICAgICAgICAgICAgIHNjb3BlPVwiY29sXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC14cyBmb250LW1lZGl1bSB0cmFja2luZy13aWRlciB0ZXh0LWxlZnQgdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgbGc6cHgtNlwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBBY3Rpb25zXG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgIDx0aFxuICAgICAgICAgICAgICAgIHNjb3BlPVwiY29sXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC14cyBmb250LW1lZGl1bSB0cmFja2luZy13aWRlciB0ZXh0LWxlZnQgdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgbGc6cHgtNlwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBFeHBpcnlcbiAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICB7KHNlYXJjaC50cmltKCkgPyBzaXRlcy5maWx0ZXIoc2l0ZSA9PiBzaXRlLnNpdGVfbmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaC50b0xvd2VyQ2FzZSgpKSkgOiBzaXRlcykubWFwKChzaXRlKSA9PiAoXG4gICAgICAgICAgICAgIDx0ciBrZXk9e3NpdGUuaWR9PlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGxnOnB4LTYgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgIHtzaXRlLnNpdGVfbmFtZX1cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwibWwtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMCA2SDZhMiAyIDAgMDAtMiAydjEwYTIgMiAwIDAwMiAyaDEwYTIgMiAwIDAwMi0ydi00TTE0IDRoNm0wIDB2Nm0wLTZMMTAgMTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC00IHB5LTQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXJpZ2h0IGxnOnB4LTYgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1lbmQgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBNYXAgRG9tYWluIEljb24gLSBHbG9iZSBpY29uLCBhbHdheXMgdmlzaWJsZSAqL31cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSB0ZXh0LWJsdWUtNTAwIGhvdmVyOnRleHQtYmx1ZS03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiTWFwIERvbWFpblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4geyBzZXRTZWxlY3RlZFNpdGVOYW1lKHNpdGUuc2l0ZV9uYW1lKTsgc2V0SXNNYXBEb21haW5PcGVuKHRydWUpOyB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwidy00IGgtNCBsZzp3LTUgbGc6aC01XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMy4wNTUgMTFINWEyIDIgMCAwMTIgMnYxYTIgMiAwIDAwMiAyIDIgMiAwIDAxMiAydjIuOTQ1TTggMy45MzVWNS41QTIuNSAyLjUgMCAwMDEwLjUgOGguNWEyIDIgMCAwMTIgMiAyIDIgMCAxMDQgMCAyIDIgMCAwMTItMmgxLjA2NE0xNSAyMC40ODhWMThhMiAyIDAgMDEyLTJoMy4wNjRNMjEgMTJhOSA5IDAgMTEtMTggMCA5IDkgMCAwMTE4IDB6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBHbyBMaXZlIEljb24gLSBSb2NrZXQgaWNvbiwgb25seSBmb3IgVGVtcG9yYXJ5IHNpdGVzICovfVxuICAgICAgICAgICAgICAgICAgICB7c2l0ZS5leHBpcnlfc3RhdHVzID09PSAnVGVtcG9yYXJ5JyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xIHRleHQtZ3JlZW4tNTAwIGhvdmVyOnRleHQtZ3JlZW4tNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ2hvb3NlIFBsYW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4geyB3aW5kb3cubG9jYXRpb24uaHJlZiA9IGAvcGF5bWVudHM/c2l0ZUlkPSR7c2l0ZS5pZH1gIH19XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwidy00IGgtNCBsZzp3LTUgbGc6aC01XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyAxMFYzTDQgMTRoN3Y3bDktMTFoLTd6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICB7LyogQ2hhbmdlIFBsYW4gSWNvbiAtIENyZWRpdCBjYXJkIGljb24sIG9ubHkgZm9yIFBlcm1hbmVudCBzaXRlcyAqL31cbiAgICAgICAgICAgICAgICAgICAge3NpdGUuZXhwaXJ5X3N0YXR1cyA9PT0gJ1Blcm1hbmVudCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSB0ZXh0LXB1cnBsZS01MDAgaG92ZXI6dGV4dC1wdXJwbGUtNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ2hhbmdlIFBsYW5cIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGNsYXNzTmFtZT1cInctNCBoLTQgbGc6dy01IGxnOmgtNVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PVwiMlwiIHk9XCI3XCIgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjEwXCIgcng9XCIyXCIgcnk9XCIyXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCIgZmlsbD1cIm5vbmVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIgMTFoMjBcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiN1wiIGN5PVwiMTVcIiByPVwiMVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiMTFcIiBjeT1cIjE1XCIgcj1cIjFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS00IHRleHQtc20gbGc6cHgtNiB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgey8qIEV4cGlyeSBiYWRnZSB3aXRoIHRvb2x0aXAgZm9yIFRlbXBvcmFyeSBzaXRlcyAqL31cbiAgICAgICAgICAgICAgICAgIHtzaXRlLmV4cGlyeV9zdGF0dXMgPT09ICdUZW1wb3JhcnknID8gKFxuICAgICAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTIgaW5saW5lLWZsZXggdGV4dC14cyBsZWFkaW5nLTUgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMGB9XG4gICAgICAgICAgICAgICAgICAgICAgdGl0bGU9eygoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXNpdGUuZXhwaXJ5X3RpbWUpIHJldHVybiAnTm8gZXhwaXJ5IHRpbWUgc2V0JztcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4cGlyeSA9IG5ldyBEYXRlKHNpdGUuZXhwaXJ5X3RpbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGRpZmZNcyA9IGV4cGlyeS5nZXRUaW1lKCkgLSBub3cuZ2V0VGltZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRpZmZNcyA8PSAwKSByZXR1cm4gJ0V4cGlyZWQnO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGlmZkRheXMgPSBNYXRoLmZsb29yKGRpZmZNcyAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkaWZmSG91cnMgPSBNYXRoLmZsb29yKChkaWZmTXMgJSAoMTAwMCAqIDYwICogNjAgKiAyNCkpIC8gKDEwMDAgKiA2MCAqIDYwKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYCR7ZGlmZkRheXN9IGRheXMgJHtkaWZmSG91cnN9IGhvdXJzIGxlZnQgKGV4cGlyZXMgYXQgJHtleHBpcnkudG9Mb2NhbGVTdHJpbmcoKX0pYDtcbiAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3NpdGUuZXhwaXJ5X3N0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0yIGlubGluZS1mbGV4IHRleHQteHMgbGVhZGluZy01IGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsIGJnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMGB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2l0ZS5leHBpcnlfc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICA8L3RhYmxlPlxuICAgICAgICB7LyogRGVza3RvcCBUYWJsZSBGb290ZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHRleHQtc20gdGV4dC1ncmF5LTYwMCBiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1iLWxnXCI+XG4gICAgICAgICAge3NpdGVzLmxlbmd0aH0gU2l0ZXNcbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1vYmlsZSBDYXJkIExheW91dCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHNwYWNlLXktNCBtZDpoaWRkZW5cIj5cbiAgICAgICAgeyhzZWFyY2gudHJpbSgpID8gc2l0ZXMuZmlsdGVyKHNpdGUgPT4gc2l0ZS5zaXRlX25hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2gudG9Mb3dlckNhc2UoKSkpIDogc2l0ZXMpLm1hcCgoc2l0ZSkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXtzaXRlLmlkfSBjbGFzc05hbWU9XCJwLTQgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57c2l0ZS5zaXRlX25hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMCA2SDZhMiAyIDAgMDAtMiAydjEwYTIgMiAwIDAwMiAyaDEwYTIgMiAwIDAwMi0ydi00TTE0IDRoNm0wIDB2Nm0wLTZMMTAgMTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgey8qIEV4cGlyeSBiYWRnZSAqL31cbiAgICAgICAgICAgICAge3NpdGUuZXhwaXJ5X3N0YXR1cyA9PT0gJ1RlbXBvcmFyeScgPyAoXG4gICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTIgaW5saW5lLWZsZXggdGV4dC14cyBsZWFkaW5nLTUgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMGB9XG4gICAgICAgICAgICAgICAgICB0aXRsZT17KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFzaXRlLmV4cGlyeV90aW1lKSByZXR1cm4gJ05vIGV4cGlyeSB0aW1lIHNldCc7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4cGlyeSA9IG5ldyBEYXRlKHNpdGUuZXhwaXJ5X3RpbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkaWZmTXMgPSBleHBpcnkuZ2V0VGltZSgpIC0gbm93LmdldFRpbWUoKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGRpZmZNcyA8PSAwKSByZXR1cm4gJ0V4cGlyZWQnO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkaWZmRGF5cyA9IE1hdGguZmxvb3IoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGlmZkhvdXJzID0gTWF0aC5mbG9vcigoZGlmZk1zICUgKDEwMDAgKiA2MCAqIDYwICogMjQpKSAvICgxMDAwICogNjAgKiA2MCkpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYCR7ZGlmZkRheXN9IGRheXMgJHtkaWZmSG91cnN9IGhvdXJzIGxlZnQgKGV4cGlyZXMgYXQgJHtleHBpcnkudG9Mb2NhbGVTdHJpbmcoKX0pYDtcbiAgICAgICAgICAgICAgICAgIH0pKCl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge3NpdGUuZXhwaXJ5X3N0YXR1c31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTIgaW5saW5lLWZsZXggdGV4dC14cyBsZWFkaW5nLTUgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwYH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c2l0ZS5leHBpcnlfc3RhdHVzfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQWN0aW9uIGJ1dHRvbnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktc3RhcnQgcHQtMyBzcGFjZS14LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgIHsvKiBNYXAgRG9tYWluIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBzcGFjZS14LTIgdGV4dC1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9ycyByb3VuZGVkLW1kIGhvdmVyOnRleHQtYmx1ZS03MDAgaG92ZXI6YmctYmx1ZS01MFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4geyBzZXRTZWxlY3RlZFNpdGVOYW1lKHNpdGUuc2l0ZV9uYW1lKTsgc2V0SXNNYXBEb21haW5PcGVuKHRydWUpOyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTMuMDU1IDExSDVhMiAyIDAgMDEyIDJ2MWEyIDIgMCAwMDIgMiAyIDIgMCAwMTIgMnYyLjk0NU04IDMuOTM1VjUuNUEyLjUgMi41IDAgMDAxMC41IDhoLjVhMiAyIDAgMDEyIDIgMiAyIDAgMTA0IDAgMiAyIDAgMDEyLTJoMS4wNjRNMTUgMjAuNDg4VjE4YTIgMiAwIDAxMi0yaDMuMDY0TTIxIDEyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPk1hcCBEb21haW48L3NwYW4+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgIHsvKiBHbyBMaXZlIEJ1dHRvbiAtIG9ubHkgZm9yIFRlbXBvcmFyeSBzaXRlcyAqL31cbiAgICAgICAgICAgICAge3NpdGUuZXhwaXJ5X3N0YXR1cyA9PT0gJ1RlbXBvcmFyeScgJiYgKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBzcGFjZS14LTIgdGV4dC1ncmVlbi01MDAgdHJhbnNpdGlvbi1jb2xvcnMgcm91bmRlZC1tZCBob3Zlcjp0ZXh0LWdyZWVuLTcwMCBob3ZlcjpiZy1ncmVlbi01MFwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYC9wYXltZW50cz9zaXRlSWQ9JHtzaXRlLmlkfWAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyAxMFYzTDQgMTRoN3Y3bDktMTFoLTd6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkdvIExpdmU8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIENoYW5nZSBQbGFuIEJ1dHRvbiAtIG9ubHkgZm9yIFBlcm1hbmVudCBzaXRlcyAqL31cbiAgICAgICAgICAgICAge3NpdGUuZXhwaXJ5X3N0YXR1cyA9PT0gJ1Blcm1hbmVudCcgJiYgKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBzcGFjZS14LTIgdGV4dC1wdXJwbGUtNTAwIHRyYW5zaXRpb24tY29sb3JzIHJvdW5kZWQtbWQgaG92ZXI6dGV4dC1wdXJwbGUtNzAwIGhvdmVyOmJnLXB1cnBsZS01MFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PVwiMlwiIHk9XCI3XCIgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjEwXCIgcng9XCIyXCIgcnk9XCIyXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCIgZmlsbD1cIm5vbmVcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIgMTFoMjBcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiN1wiIGN5PVwiMTVcIiByPVwiMVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiMTFcIiBjeT1cIjE1XCIgcj1cIjFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkNoYW5nZSBQbGFuPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuXG4gICAgICAgIHsvKiBNb2JpbGUgRm9vdGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCB0ZXh0LXNtIHRleHQtY2VudGVyIHRleHQtZ3JheS02MDAgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWRcIj5cbiAgICAgICAgICB7c2l0ZXMubGVuZ3RofSBTaXRlc1xuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPE1hcERvbWFpbk1vZGFsIFxuICAgICAgICBpc09wZW49e2lzTWFwRG9tYWluT3Blbn0gXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzTWFwRG9tYWluT3BlbihmYWxzZSl9IFxuICAgICAgICBzaXRlTmFtZT17c2VsZWN0ZWRTaXRlTmFtZX0gXG4gICAgICAvPlxuXG4gICAgICA8QXNrRG9tYWluTW9kYWxcbiAgICAgICAgaXNPcGVuPXtpc0Fza0RvbWFpbk9wZW59XG4gICAgICAgIG9uWWVzPXsoKSA9PiB7XG4gICAgICAgICAgLy8gb3BlbiBtYXAgZG9tYWluIG1vZGFsXG4gICAgICAgICAgY29uc3Qgc2l0ZSA9IHNpdGVzLmZpbmQocyA9PiBzLmlkID09PSBzZWxlY3RlZFNpdGVJZCk7XG4gICAgICAgICAgaWYgKHNpdGUpIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkU2l0ZU5hbWUoc2l0ZS5zaXRlX25hbWUpO1xuICAgICAgICAgICAgc2V0SXNNYXBEb21haW5PcGVuKHRydWUpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBzZXRJc0Fza0RvbWFpbk9wZW4oZmFsc2UpO1xuICAgICAgICB9fVxuICAgICAgICBvbk5vPXsoKSA9PiB7XG4gICAgICAgICAgcm91dGVyLnB1c2goYC9kYXNoYm9hcmQvZG9tYWluYCk7XG4gICAgICAgICAgc2V0SXNBc2tEb21haW5PcGVuKGZhbHNlKTtcbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBEYXNoYm9hcmRQYWdlOyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50IiwiTWFwRG9tYWluTW9kYWwiLCJBc2tEb21haW5Nb2RhbCIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsInVzZUF1dGgiLCJEYXNoYm9hcmRQYWdlIiwic2VhcmNoUGFyYW1zIiwicm91dGVyIiwidXNlciIsImxvYWRpbmciLCJzaXRlcyIsInNldFNpdGVzIiwic2l0ZXNMb2FkaW5nIiwic2V0U2l0ZXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNlYXJjaCIsInNldFNlYXJjaCIsImlzTWFwRG9tYWluT3BlbiIsInNldElzTWFwRG9tYWluT3BlbiIsImlzQXNrRG9tYWluT3BlbiIsInNldElzQXNrRG9tYWluT3BlbiIsInNlbGVjdGVkU2l0ZU5hbWUiLCJzZXRTZWxlY3RlZFNpdGVOYW1lIiwic2VsZWN0ZWRTaXRlSWQiLCJzZXRTZWxlY3RlZFNpdGVJZCIsInN1cGFiYXNlIiwicmVwbGFjZSIsInBvc3RDaGVja291dCIsImdldCIsInNpdGVJZEZyb21QYXJhbSIsImZldGNoU2l0ZXMiLCJkYXRhIiwiZnJvbSIsInNlbGVjdCIsImVyciIsImNvbnNvbGUiLCJFcnJvciIsIm1lc3NhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInhtbG5zIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwic2NvcGUiLCJ0Ym9keSIsInRyaW0iLCJmaWx0ZXIiLCJzaXRlIiwic2l0ZV9uYW1lIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsIm1hcCIsInRkIiwiYSIsImhyZWYiLCJidXR0b24iLCJ0aXRsZSIsIm9uQ2xpY2siLCJleHBpcnlfc3RhdHVzIiwid2luZG93IiwibG9jYXRpb24iLCJpZCIsInJlY3QiLCJ4IiwieSIsIndpZHRoIiwiaGVpZ2h0IiwicngiLCJyeSIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwic3BhbiIsImV4cGlyeV90aW1lIiwiZXhwaXJ5IiwiRGF0ZSIsIm5vdyIsImRpZmZNcyIsImdldFRpbWUiLCJkaWZmRGF5cyIsIk1hdGgiLCJmbG9vciIsImRpZmZIb3VycyIsInRvTG9jYWxlU3RyaW5nIiwibGVuZ3RoIiwiaDMiLCJpc09wZW4iLCJvbkNsb3NlIiwic2l0ZU5hbWUiLCJvblllcyIsImZpbmQiLCJzIiwib25ObyIsInB1c2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});